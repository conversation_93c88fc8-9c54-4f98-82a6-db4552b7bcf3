import { expect, test } from '@playwright/test'

test.describe('订单筛选功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 准备：登录到系统
    await page.goto('/')

    // 等待登录页面加载
    await expect(page).toHaveURL('/login', { timeout: 10000 })

    // 填写登录信息（使用与其他测试相同的账号）
    await page.getByPlaceholder('请输入用户名').fill('<EMAIL>')
    await page.getByPlaceholder('请输入密码').fill('K9x#mN8p$L2w@Q5t')

    // 等待登录API请求
    const [response] = await Promise.all([
      page.waitForResponse(
        (response) =>
          response.url().includes('/api/login') && response.request().method() === 'POST',
      ),
      page.getByRole('button', { name: '登录' }).click(),
    ])

    console.log(`登录API响应状态: ${response.status()}`)

    // 等待登录成功 - 更严格的检查
    try {
      await page.waitForURL('**/visa-form', { timeout: 10000 })
      console.log('✅ 登录成功，已导航到签证表单页面')
    } catch (error) {
      console.log('⚠️ 登录可能失败或未导航到预期页面')
      // 检查当前URL
      const currentUrl = page.url()
      console.log(`当前URL: ${currentUrl}`)

      // 如果仍在登录页面，说明登录失败
      if (currentUrl.includes('/login')) {
        throw new Error('登录失败，仍在登录页面')
      }
    }

    // 等待认证状态稳定 - 增加等待时间确保Pinia状态同步
    await page.waitForTimeout(5000)

    // 验证认证状态 - 检查localStorage中的认证信息
    const authState = await page.evaluate(() => {
      const authData = localStorage.getItem('auth')
      return authData ? JSON.parse(authData) : null
    })

    console.log('认证状态:', authState)

    if (!authState || !authState.token) {
      throw new Error('认证状态未正确保存到localStorage')
    }

    // 导航到订单管理页面
    await page.goto('/order-management')
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(3000) // 增加等待时间

    // 检查是否成功导航到订单管理页面
    const currentUrl = page.url()
    console.log(`导航后当前URL: ${currentUrl}`)

    if (!currentUrl.includes('/order-management')) {
      // 如果导航失败，再次检查认证状态
      const currentAuthState = await page.evaluate(() => {
        const authData = localStorage.getItem('auth')
        return authData ? JSON.parse(authData) : null
      })
      console.log('导航失败时的认证状态:', currentAuthState)
      throw new Error(`未能导航到订单管理页面，当前URL: ${currentUrl}`)
    }

    // 等待页面核心元素加载 - 更健壮的检查
    try {
      await expect(page.locator('h2').filter({ hasText: '我的订单' })).toBeVisible({
        timeout: 15000,
      })
      console.log('✅ 订单管理页面加载成功')
    } catch (error) {
      console.log('⚠️ 页面标题未找到，检查页面结构')
      // 检查是否有其他关键元素
      const hasQueryButton = await page.locator('button').filter({ hasText: '查询' }).isVisible()
      const hasResetButton = await page.locator('button').filter({ hasText: '重置' }).isVisible()

      if (hasQueryButton && hasResetButton) {
        console.log('✅ 找到查询和重置按钮，页面应该已正确加载')
      } else {
        throw new Error('订单管理页面未正确加载，缺少关键元素')
      }
    }
  })

  test('客户筛选功能测试', async ({ page }) => {
    console.log('🔍 测试客户来源筛选功能...')

    // 根据OrderManagementView.vue源代码，查找客户来源筛选输入框
    // 使用正确的placeholder文本："输入客户名称"
    const customerSourceInput = page.getByPlaceholder('输入客户名称')

    // 检查客户筛选输入框是否存在
    await expect(customerSourceInput).toBeVisible({ timeout: 10000 })

    // 输入测试数据
    await customerSourceInput.fill('飞猪')

    // 处理可能的会话失效弹窗
    try {
      const sessionDialog = page.locator('[aria-label="重复登录提醒"]')
      if (await sessionDialog.isVisible({ timeout: 2000 })) {
        console.log('⚠️ 检测到会话失效弹窗，点击确认')
        await page.locator('button').filter({ hasText: '确定' }).click()
        await page.waitForTimeout(1000)
      }
    } catch (error) {
      // 忽略弹窗检测错误
    }

    // 点击查询按钮
    const searchButton = page.locator('button').filter({ hasText: '查询' })
    await expect(searchButton).toBeVisible({ timeout: 15000 })
    await searchButton.click()

    // 等待查询结果加载
    await page.waitForTimeout(2000)

    // 验证筛选是否工作
    // 检查是否有"无数据"提示或者有筛选结果
    const noDataElement = page.locator('.el-empty').or(page.locator('text=暂无数据'))
    const tableRows = page.locator('.el-table__row')

    const hasNoData = await noDataElement.isVisible().catch(() => false)
    const hasData = await tableRows
      .count()
      .then((count) => count > 0)
      .catch(() => false)

    if (hasNoData) {
      console.log('✅ 客户筛选功能正常 - 无匹配数据')
    } else if (hasData) {
      console.log('✅ 客户筛选功能正常 - 找到匹配数据')
    } else {
      console.log('⚠️ 客户筛选状态不明确')
    }

    // 清除筛选条件
    await customerSourceInput.clear()
    const resetButton = page.locator('button').filter({ hasText: '重置' })
    if (await resetButton.isVisible().catch(() => false)) {
      await resetButton.click()
    }
  })

  test('申请人姓名筛选功能测试', async ({ page }) => {
    console.log('🔍 测试申请人姓名筛选功能...')

    // 根据OrderManagementView.vue源代码，查找申请人姓名筛选输入框
    // 使用正确的placeholder文本："输入中文名或英文名"
    const applicantNameInput = page.getByPlaceholder('输入中文名或英文名')

    // 检查姓名筛选输入框是否存在
    await expect(applicantNameInput).toBeVisible({ timeout: 10000 })

    // 输入测试数据
    await applicantNameInput.fill('张三')

    // 处理可能的会话失效弹窗
    try {
      const sessionDialog = page.locator('[aria-label="重复登录提醒"]')
      if (await sessionDialog.isVisible({ timeout: 2000 })) {
        console.log('⚠️ 检测到会话失效弹窗，点击确认')
        await page.locator('button').filter({ hasText: '确定' }).click()
        await page.waitForTimeout(1000)
      }
    } catch (error) {
      // 忽略弹窗检测错误
    }

    // 点击查询按钮
    const searchButton = page.locator('button').filter({ hasText: '查询' })
    await expect(searchButton).toBeVisible({ timeout: 15000 })
    await searchButton.click()

    // 等待查询结果加载
    await page.waitForTimeout(2000)

    // 验证筛选是否工作
    const noDataElement = page.locator('.el-empty').or(page.locator('text=暂无数据'))
    const tableRows = page.locator('.el-table__row')

    const hasNoData = await noDataElement.isVisible().catch(() => false)
    const hasData = await tableRows
      .count()
      .then((count) => count > 0)
      .catch(() => false)

    if (hasNoData) {
      console.log('✅ 姓名筛选功能正常 - 无匹配数据')
    } else if (hasData) {
      console.log('✅ 姓名筛选功能正常 - 找到匹配数据')

      // 检查结果中是否包含搜索的姓名
      const tableContent = await page.locator('.el-table').textContent()
      if (tableContent?.includes('张三')) {
        console.log('✅ 姓名筛选结果准确')
      } else {
        console.log('⚠️ 姓名筛选结果可能不准确')
      }
    } else {
      console.log('⚠️ 姓名筛选状态不明确')
    }
  })

  test('组合筛选功能测试', async ({ page }) => {
    console.log('🔍 测试组合筛选功能...')

    // 使用正确的选择器同时使用客户来源和姓名筛选
    const customerSourceInput = page.getByPlaceholder('输入客户名称')
    const applicantNameInput = page.getByPlaceholder('输入中文名或英文名')

    await expect(customerSourceInput).toBeVisible({ timeout: 10000 })
    await expect(applicantNameInput).toBeVisible({ timeout: 10000 })

    await customerSourceInput.fill('飞猪')
    await applicantNameInput.fill('李四')

    // 处理可能的会话失效弹窗
    try {
      const sessionDialog = page.locator('[aria-label="重复登录提醒"]')
      if (await sessionDialog.isVisible({ timeout: 2000 })) {
        console.log('⚠️ 检测到会话失效弹窗，点击确认')
        await page.locator('button').filter({ hasText: '确定' }).click()
        await page.waitForTimeout(1000)
      }
    } catch (error) {
      // 忽略弹窗检测错误
    }

    // 点击查询
    const searchButton = page.locator('button').filter({ hasText: '查询' })
    await expect(searchButton).toBeVisible({ timeout: 15000 })
    await searchButton.click()

    // 等待结果
    await page.waitForTimeout(2000)

    console.log('✅ 组合筛选测试完成')
  })

  test('筛选状态保持测试', async ({ page }) => {
    console.log('🔍 测试筛选状态保持...')

    // 使用正确的选择器设置筛选条件
    const customerSourceInput = page.getByPlaceholder('输入客户名称')
    await expect(customerSourceInput).toBeVisible({ timeout: 10000 })
    await customerSourceInput.fill('测试客户')

    // 处理可能的会话失效弹窗
    try {
      const sessionDialog = page.locator('[aria-label="重复登录提醒"]')
      if (await sessionDialog.isVisible({ timeout: 2000 })) {
        console.log('⚠️ 检测到会话失效弹窗，点击确认')
        await page.locator('button').filter({ hasText: '确定' }).click()
        await page.waitForTimeout(1000)
      }
    } catch (error) {
      // 忽略弹窗检测错误
    }

    // 查询
    const searchButton = page.locator('button').filter({ hasText: '查询' })
    await expect(searchButton).toBeVisible({ timeout: 15000 })
    await searchButton.click()
    await page.waitForTimeout(1000)

    // 检查筛选条件是否保持
    const inputValue = await customerSourceInput.inputValue()
    expect(inputValue).toBe('测试客户')

    console.log('✅ 筛选状态保持测试完成')
  })

  test('重置功能测试', async ({ page }) => {
    console.log('🔍 测试重置功能...')

    // 使用正确的选择器填写筛选条件
    const customerSourceInput = page.getByPlaceholder('输入客户名称')
    const applicantNameInput = page.getByPlaceholder('输入中文名或英文名')

    await expect(customerSourceInput).toBeVisible({ timeout: 10000 })
    await expect(applicantNameInput).toBeVisible({ timeout: 10000 })

    await customerSourceInput.fill('测试客户')
    await applicantNameInput.fill('测试姓名')

    // 处理可能的会话失效弹窗
    try {
      const sessionDialog = page.locator('[aria-label="重复登录提醒"]')
      if (await sessionDialog.isVisible({ timeout: 2000 })) {
        console.log('⚠️ 检测到会话失效弹窗，点击确认')
        await page.locator('button').filter({ hasText: '确定' }).click()
        await page.waitForTimeout(1000)
      }
    } catch (error) {
      // 忽略弹窗检测错误
    }

    // 点击重置按钮
    const resetButton = page.locator('button').filter({ hasText: '重置' })
    await expect(resetButton).toBeVisible({ timeout: 15000 })
    await resetButton.click()

    // 等待重置完成
    await page.waitForTimeout(1000)

    // 验证所有输入框都被清空
    await expect(customerSourceInput).toHaveValue('')
    await expect(applicantNameInput).toHaveValue('')

    console.log('✅ 重置功能测试完成')
  })
})
